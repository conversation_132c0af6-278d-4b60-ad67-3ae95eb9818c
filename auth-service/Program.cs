using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Text;
using auth_service.Services;

var builder = WebApplication.CreateBuilder(args);

// ASP.NET Core automatically loads environment variables - no additional setup needed
Console.WriteLine("DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)");

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
if (builder.Environment.IsDevelopment())
{
    builder.Logging.AddDebug();
}

// Configure CORS for frontend access
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Configure JWT authentication for Supabase using JWT secret
// Clear default claim type mappings to use original claim names
JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();

// Configure JWT authentication for Supabase using environment variables
// Environment variables automatically override appsettings.json values
var supabaseUrl = builder.Configuration["SUPABASE_URL"];
var supabaseJwtSecret = builder.Configuration["SUPABASE_JWT_SECRET"];

// Log configuration status for debugging
Console.WriteLine($"DEBUG: Environment: {builder.Environment.EnvironmentName}");
Console.WriteLine($"DEBUG: ✅ Configuration loaded from ASP.NET Core configuration");

// Skip validation in test environments
var isTestEnvironment = builder.Environment.EnvironmentName == "Testing";

if (!isTestEnvironment && string.IsNullOrEmpty(supabaseUrl))
{
    throw new InvalidOperationException("SUPABASE_URL must be configured");
}

if (!isTestEnvironment && string.IsNullOrEmpty(supabaseJwtSecret))
{
    throw new InvalidOperationException("SUPABASE_JWT_SECRET must be configured");
}

// Use default test values if in test environment and values are not set
if (isTestEnvironment)
{
    supabaseUrl ??= "https://test.supabase.co";
    supabaseJwtSecret ??= "test-jwt-secret-key-for-testing-purposes-only-not-for-production-use";
}

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        // Use JWT secret for Supabase token validation
        if (string.IsNullOrEmpty(supabaseJwtSecret))
        {
            throw new InvalidOperationException("JWT secret cannot be null or empty at this point");
        }
        var key = Encoding.UTF8.GetBytes(supabaseJwtSecret);

        options.RequireHttpsMetadata = !builder.Environment.IsDevelopment(); // Allow HTTP in development
        options.SaveToken = true;

        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = false, // Temporarily disable for testing
            ValidateAudience = false, // Temporarily disable for testing
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(key),
            ClockSkew = TimeSpan.FromMinutes(5)
        };

        // Enhanced event handlers for debugging and monitoring
        options.Events = new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                logger.LogWarning("JWT Authentication failed: {Error}", context.Exception.Message);
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                var userId = context.Principal?.FindFirst("sub")?.Value;
                var email = context.Principal?.FindFirst("email")?.Value;
                logger.LogInformation("JWT Token validated for user: {UserId} ({Email})", userId, email);
                return Task.CompletedTask;
            }
        };
    });

builder.Services.AddHttpClient<ISupabaseClient, SupabaseClient>();

// Add memory cache for performance optimization
builder.Services.AddMemoryCache();
builder.Services.AddScoped<ICacheService, MemoryCacheService>();

// Add authorization with role-based policies
builder.Services.AddAuthorization(options =>
{
    // Example role-based policy
    options.AddPolicy("IsTrader", policy =>
        policy.RequireClaim("role", "trader"));

    options.AddPolicy("IsAdmin", policy =>
        policy.RequireClaim("role", "admin"));

    // Custom policy for broker-linked users
    options.AddPolicy("HasBroker", policy =>
        policy.RequireAssertion(context =>
            context.User.HasClaim(c => c.Type == "broker_id" && !string.IsNullOrEmpty(c.Value))));
});
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "Auth Service API", Version = "v1" });
    c.AddSecurityDefinition("Bearer", new()
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    c.AddSecurityRequirement(new()
    {
        {
            new()
            {
                Reference = new() { Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme, Id = "Bearer" }
            },
            new string[] {}
        }
    });
});
builder.Services.AddHealthChecks();

var app = builder.Build();

// Test comment for deployment detection

// Configure middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Auth Service API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

// Add error handling endpoint
app.Map("/error", (ILogger<Program> logger) =>
{
    logger.LogError("An unhandled exception occurred in Auth service");
    return Results.Problem("An error occurred while processing your request.");
});

// Add a root endpoint for API information
app.MapGet("/", () => new
{
    service = "Auth Service",
    version = "1.0.0",
    endpoints = new
    {
        authentication = new[]
        {
            "POST /api/auth/signup - User signup",
            "POST /api/auth/login - User login",
            "POST /api/auth/token/refresh - Refresh access token",
            "POST /api/auth/recover - Password recovery",
            "POST /api/auth/otp - Send OTP",
            "POST /api/auth/verify - Verify OTP",
            "POST /api/auth/logout - User logout"
        },
        user_management = new[]
        {
            "GET /api/auth/user - Get current user",
            "PUT /api/auth/user - Update user",
            "GET /api/auth/profile - Get user profile",
            "POST /api/auth/profile/link-broker - Link broker to profile"
        },
        user_info = new[]
        {
            "GET /api/user/me - Get current user from JWT claims",
            "GET /api/user/profile - Get user profile",
            "GET /api/user/role/{roleName} - Check if user has specific role",
            "GET /api/user/trader-only - Trader-only endpoint (requires IsTrader policy)",
            "GET /api/user/admin-only - Admin-only endpoint (requires IsAdmin policy)",
            "GET /api/user/broker-required - Requires linked broker (HasBroker policy)"
        },
        oauth = new[]
        {
            "POST /api/auth/oauth - Get OAuth URL",
            "POST /api/auth/oauth/callback - OAuth callback"
        },
        settings = new[]
        {
            "GET /api/auth/settings - Get auth settings"
        },
        health = new[]
        {
            "GET /health - Health check"
        }
    }
});

app.Run();

// Make the implicit Program class public for testing
public partial class Program { }
using auth_service.Models;

namespace auth_service.Services;

public interface ISupabaseClient
{
    // Profile Management (using Supabase managed auth)
    Task<SupabaseUser?> GetProfileAsync(string userId);
    Task<bool> LinkBrokerAsync(string userId, string brokerId);

    // Authentication Methods
    Task<AuthResponse> SignupAsync(SignupRequest request);
    Task<AuthResponse> LoginAsync(LoginRequest request);
    Task<AuthResponse> RefreshTokenAsync(TokenRefreshRequest request);
    Task<SuccessResponse> RecoverPasswordAsync(PasswordRecoveryRequest request);
    Task<SuccessResponse> SendOtpAsync(OtpRequest request);
    Task<AuthResponse> VerifyOtpAsync(VerifyRequest request);
    Task<SupabaseUser> GetUserAsync(string accessToken);
    Task<SupabaseUser> UpdateUserAsync(string accessToken, UpdateUserRequest request);
    Task<SuccessResponse> LogoutAsync(string accessToken);

    // OAuth Methods
    Task<OAuthResponse> GetOAuthUrlAsync(OAuthRequest request);
    Task<AuthResponse> ExchangeCodeForTokenAsync(string code, string provider);

    // Google Sign-In Methods
    Task<OAuthResponse> GetGoogleSignInUrlAsync(string? redirectTo = null);
    Task<AuthResponse> SignInWithGoogleAsync(string code);

    // Phone Sign-In Methods (Twilio)
    Task<SuccessResponse> SendPhoneOtpAsync(string phoneNumber, bool createUser = true);
    Task<AuthResponse> SignInWithPhoneAsync(string phoneNumber, string otpCode);

    // Settings
    Task<AuthSettingsResponse> GetSettingsAsync();
}

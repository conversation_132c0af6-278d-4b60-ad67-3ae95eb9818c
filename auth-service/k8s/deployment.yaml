apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: abraapi
  labels:
    app: auth-service
    service: auth
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
        service: auth
        version: v1
    spec:
      containers:
      - name: auth-service
        image: ghcr.io/abraapp/auth-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: auth-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: auth-service-config
              key: ASPNETCORE_URLS
        - name: SUPABASE_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_JWT_SECRET
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_URL
        - name: SUPABASE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_PROJECT_ID
        - name: SUPABASE_SERVICE_ROLE_KEY
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_SERVICE_ROLE_KEY
        - name: SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_ANON_KEY
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 180
          periodSeconds: 45
          timeoutSeconds: 30
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL

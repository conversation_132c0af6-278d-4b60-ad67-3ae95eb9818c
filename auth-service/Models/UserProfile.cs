using System.Text.Json.Serialization;

namespace auth_service.Models;

// Simplified UserProfile for response DTOs - data comes from Supabase managed auth
public class UserProfileResponse
{
    [JsonPropertyName("user_id")]
    public string UserId { get; set; } = string.Empty;

    [JsonPropertyName("email")]
    public string Email { get; set; } = string.Empty;

    [JsonPropertyName("phone")]
    public string? Phone { get; set; }

    [JsonPropertyName("full_name")]
    public string? FullName { get; set; }

    [JsonPropertyName("username")]
    public string? Username { get; set; }

    [JsonPropertyName("broker_id")]
    public string? BrokerId { get; set; }

    [Json<PERSON>ropertyName("verified")]
    public bool Verified { get; set; }

    [Json<PERSON>ropertyName("created_at")]
    public DateTime? CreatedAt { get; set; }
}


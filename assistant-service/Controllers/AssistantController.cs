using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using AssistantService.Models;
using AssistantService.Services;
using AssistantService.Configuration;

namespace AssistantService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AssistantController(IOllamaClient ollama, IPromptInjectionDetector injectionDetector, IContentFilter contentFilter, IOptions<SecurityOptions> securityOptions) : ControllerBase
{
    private readonly IOllamaClient _ollama = ollama;
    private readonly IPromptInjectionDetector _injectionDetector = injectionDetector;
    private readonly IContentFilter _contentFilter = contentFilter;
    private readonly SecurityOptions _securityOptions = securityOptions.Value;

    [HttpPost("ask")]
    public async Task<ActionResult<AskResponse>> Ask([FromBody] AskRequest request)
    {
        // Validate input
        if (request == null)
        {
            return BadRequest("Request cannot be empty");
        }

        string sanitizedPrompt = request.Prompt;

        // Check for prompt injection attacks (only if enabled)
        if (_securityOptions.EnablePromptInjectionDetection)
        {
            var injectionResult = _injectionDetector.AnalyzeInput(request.Prompt);

            if (injectionResult.RiskLevel == RiskLevel.Critical)
            {
                return BadRequest("Request blocked due to security concerns");
            }

            if (injectionResult.RiskLevel == RiskLevel.High)
            {
                return BadRequest("Request contains potentially harmful content");
            }

            sanitizedPrompt = injectionResult.SanitizedInput ?? request.Prompt;
        }

        // Apply content filtering (only if enabled)
        if (_securityOptions.EnableContentFiltering)
        {
            var contentResult = _contentFilter.FilterContent(sanitizedPrompt);

            if (!contentResult.IsContentSafe)
            {
                return BadRequest($"Content policy violation: {contentResult.Reason}");
            }

            sanitizedPrompt = contentResult.FilteredContent ?? string.Empty;
        }

        if (string.IsNullOrWhiteSpace(sanitizedPrompt))
        {
            return BadRequest("Prompt cannot be empty");
        }

        if (sanitizedPrompt.Length > _securityOptions.MaxInputLength)
        {
            return BadRequest($"Prompt is too long (max {_securityOptions.MaxInputLength} characters)");
        }

        try
        {
            var reply = await _ollama.AskAsync(sanitizedPrompt);
            return Ok(new AskResponse { Reply = reply });
        }
        catch (InvalidOperationException ex)
        {
            return StatusCode(503, ex.Message);
        }
        catch (Exception)
        {
            return StatusCode(500, "Something went wrong. Please try again.");
        }
    }


}

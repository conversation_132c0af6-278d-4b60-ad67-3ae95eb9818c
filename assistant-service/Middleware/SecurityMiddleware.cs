using AssistantService.Services;
using AssistantService.Configuration;
using Microsoft.Extensions.Options;
using System.Net;
using System.Text.Json;

namespace AssistantService.Middleware;

public class SecurityMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IRateLimitingService _rateLimitingService;
    private readonly ILogger<SecurityMiddleware> _logger;
    private readonly SecurityOptions _securityOptions;

    public SecurityMiddleware(RequestDelegate next, IRateLimitingService rateLimitingService, ILogger<SecurityMiddleware> logger, IOptions<SecurityOptions> securityOptions)
    {
        _next = next;
        _rateLimitingService = rateLimitingService;
        _logger = logger;
        _securityOptions = securityOptions.Value;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Get client identifier (IP address or user ID)
            var clientId = GetClientIdentifier(context);
            var endpoint = context.Request.Path.Value ?? string.Empty;

            // Apply security headers
            ApplySecurityHeaders(context);

            // Check rate limiting for API endpoints (only if enabled)
            if (IsApiEndpoint(endpoint) && _securityOptions.EnableRateLimiting)
            {
                var rateLimitResult = _rateLimitingService.CheckRateLimit(clientId, endpoint);

                if (!rateLimitResult.IsAllowed)
                {
                    await HandleRateLimitExceeded(context, rateLimitResult);
                    return;
                }

                // Record the request for rate limiting
                _rateLimitingService.RecordRequest(clientId, endpoint);

                // Add rate limit headers to response
                AddRateLimitHeaders(context, rateLimitResult);
            }

            // Log security-relevant requests
            LogSecurityEvent(context, clientId, endpoint);

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in security middleware");
            await HandleSecurityError(context);
        }
    }

    private static string GetClientIdentifier(HttpContext context)
    {
        // Try to get user ID from authentication context first
        var userId = context.User?.Identity?.Name;
        if (!string.IsNullOrEmpty(userId))
        {
            return $"user:{userId}";
        }

        // Fall back to IP address
        var ipAddress = context.Connection.RemoteIpAddress?.ToString();
        if (!string.IsNullOrEmpty(ipAddress))
        {
            return $"ip:{ipAddress}";
        }

        // Last resort: use a combination of headers
        var userAgent = context.Request.Headers.UserAgent.ToString();
        var forwarded = context.Request.Headers["X-Forwarded-For"].ToString();
        
        return $"anonymous:{(forwarded + userAgent).GetHashCode():X}";
    }

    private static void ApplySecurityHeaders(HttpContext context)
    {
        var response = context.Response;

        // Prevent clickjacking
        response.Headers["X-Frame-Options"] = "DENY";
        
        // Prevent MIME type sniffing
        response.Headers["X-Content-Type-Options"] = "nosniff";
        
        // Enable XSS protection
        response.Headers["X-XSS-Protection"] = "1; mode=block";
        
        // Strict transport security (if HTTPS)
        if (context.Request.IsHttps)
        {
            response.Headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains";
        }
        
        // Content Security Policy
        response.Headers["Content-Security-Policy"] = "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline';";
        
        // Referrer policy
        response.Headers["Referrer-Policy"] = "strict-origin-when-cross-origin";
        
        // Permissions policy
        response.Headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()";
    }

    private static bool IsApiEndpoint(string endpoint)
    {
        return endpoint.StartsWith("/api/", StringComparison.OrdinalIgnoreCase);
    }

    private async Task HandleRateLimitExceeded(HttpContext context, RateLimitResult rateLimitResult)
    {
        context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "Rate limit exceeded",
            message = rateLimitResult.Reason,
            retryAfter = (int)rateLimitResult.ResetTime.TotalSeconds,
            limitType = rateLimitResult.LimitType.ToString()
        };

        var json = JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(json);

        _logger.LogWarning("Rate limit exceeded for client. Reason: {Reason}, Reset in: {ResetTime}s", 
            rateLimitResult.Reason, rateLimitResult.ResetTime.TotalSeconds);
    }

    private static void AddRateLimitHeaders(HttpContext context, RateLimitResult rateLimitResult)
    {
        context.Response.Headers["X-RateLimit-Remaining"] = rateLimitResult.RequestsRemaining.ToString();
        context.Response.Headers["X-RateLimit-Reset"] = ((DateTimeOffset.UtcNow.Add(rateLimitResult.ResetTime)).ToUnixTimeSeconds()).ToString();
        context.Response.Headers["X-RateLimit-Limit"] = "Varies by endpoint";
    }

    private void LogSecurityEvent(HttpContext context, string clientId, string endpoint)
    {
        // Log potentially suspicious patterns
        var userAgent = context.Request.Headers.UserAgent.ToString();
        var contentLength = context.Request.ContentLength ?? 0;

        // Log requests with suspicious characteristics
        if (contentLength > 10000 || 
            userAgent.Contains("bot", StringComparison.OrdinalIgnoreCase) ||
            userAgent.Contains("crawler", StringComparison.OrdinalIgnoreCase) ||
            string.IsNullOrEmpty(userAgent))
        {
            _logger.LogInformation("Suspicious request detected. Client: {ClientId}, Endpoint: {Endpoint}, UserAgent: {UserAgent}, ContentLength: {ContentLength}", 
                clientId, endpoint, userAgent, contentLength);
        }

        // Log all API requests for monitoring
        if (IsApiEndpoint(endpoint))
        {
            _logger.LogDebug("API request: {Method} {Endpoint} from {ClientId}", 
                context.Request.Method, endpoint, clientId);
        }
    }

    private async Task HandleSecurityError(HttpContext context)
    {
        if (!context.Response.HasStarted)
        {
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            context.Response.ContentType = "application/json";

            var response = new
            {
                error = "Security error",
                message = "A security-related error occurred while processing your request."
            };

            var json = JsonSerializer.Serialize(response);
            await context.Response.WriteAsync(json);
        }
    }
}

// Extension method to register the middleware
public static class SecurityMiddlewareExtensions
{
    public static IApplicationBuilder UseSecurityMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<SecurityMiddleware>();
    }
}

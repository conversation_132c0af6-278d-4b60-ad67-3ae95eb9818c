apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: assistant-service-ingress
  namespace: abraapi
  annotations:
    kubernetes.io/ingress.class: "traefik"
    traefik.ingress.kubernetes.io/router.entrypoints: web,websecure
    traefik.ingress.kubernetes.io/router.tls: "true"
    traefik.ingress.kubernetes.io/router.tls.certresolver: "letsencrypt"
    traefik.ingress.kubernetes.io/router.middlewares: "default-redirect-https@kubernetescrd"
    nginx.ingress.kubernetes.io/health-check-path: "/health"
    nginx.ingress.kubernetes.io/health-check-interval-seconds: "30"
    nginx.ingress.kubernetes.io/load-balance: "round_robin"
    nginx.ingress.kubernetes.io/rate-limit-connections: "100"
    nginx.ingress.kubernetes.io/rate-limit-requests-per-second: "50"
spec:
  tls:
  - hosts:
    - abraapp.undeclab.com
    secretName: abraapi-tls
  rules:
  - host: abraapp.undeclab.com
    http:
      paths:
      - path: /assistant
        pathType: Prefix
        backend:
          service:
            name: assistant-service
            port:
              number: 8080
      - path: /health
        pathType: Exact
        backend:
          service:
            name: assistant-service
            port:
              number: 8080

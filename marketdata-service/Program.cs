using System.IdentityModel.Tokens.Jwt;
using System.Text;
using MarketDataService.Brokers;
using MarketDataService.Configuration;
using MarketDataService.Data;
using MarketDataService.Interfaces;
using MarketDataService.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using StackExchange.Redis;
using DotNetEnv;

// Load .env file BEFORE creating the WebApplicationBuilder
// This ensures environment variables are available during configuration
var envPaths = new[]
{
    "../.env",           // Root level (development)
    ".env",              // Current directory (development)
    "../../.env",        // Parent directory (development)
    "/app/.env",         // Container root (production)
    "/src/.env"          // Alternative container path
};

foreach (var envPath in envPaths)
{
    if (File.Exists(envPath))
    {
        DotNetEnv.Env.Load(envPath);
        Console.WriteLine($"DEBUG: Loaded .env from: {envPath}");
        break;
    }
}

var builder = WebApplication.CreateBuilder(args);

Console.WriteLine("DEBUG: Using ASP.NET Core configuration with .env file support");

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
if (builder.Environment.IsDevelopment())
{
    builder.Logging.AddDebug();
}

// Configure CORS for frontend access
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "Market Data Service API", Version = "v1" });
    c.AddSecurityDefinition("Bearer", new()
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    c.AddSecurityRequirement(new()
    {
        {
            new()
            {
                Reference = new() { Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme, Id = "Bearer" }
            },
            new string[] {}
        }
    });
});

// Add health checks
builder.Services.AddHealthChecks();

// Configure database context using ASP.NET Core configuration
// Environment variables automatically override appsettings.json values
var connectionString = builder.Configuration["SUPABASE_CONNECTION_STRING"]
    ?? builder.Configuration.GetConnectionString("DefaultConnection")
    ?? throw new InvalidOperationException("No database connection string found. Set SUPABASE_CONNECTION_STRING environment variable or DefaultConnection in appsettings.json");

Console.WriteLine($"DEBUG: Using connection string from ASP.NET Core configuration");
Console.WriteLine($"DEBUG: Connection string: {connectionString}");
Console.WriteLine($"DEBUG: Connection string length: {connectionString.Length}");
Console.WriteLine($"DEBUG: Connection string ends with: '{connectionString.Substring(Math.Max(0, connectionString.Length - 20))}'");

// Only register PostgreSQL if not in testing environment (tests will register their own InMemory database)
if (!builder.Environment.IsEnvironment("Testing"))
{
    builder.Services.AddDbContext<MarketDataContext>(options =>
    {
        options.UseNpgsql(connectionString, npgsqlOptions =>
        {
            npgsqlOptions.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(10),
                errorCodesToAdd: null);
            npgsqlOptions.CommandTimeout(30);
        });

        if (builder.Environment.IsDevelopment())
        {
            options.EnableSensitiveDataLogging();
            options.EnableDetailedErrors();
        }
    });
}

// Configure settings
builder.Services.Configure<BrokerSettings>(builder.Configuration.GetSection("BrokerSettings"));
builder.Services.Configure<BrokerApiKeys>(builder.Configuration.GetSection("BrokerApiKeys"));

// Configure Redis connection (with fallback for development)
if (builder.Environment.IsDevelopment())
{
    // Use in-memory cache for development when Redis is not available
    builder.Services.AddMemoryCache();
    builder.Services.AddSingleton<IRedisCacheService, InMemoryCacheService>();
    // Add structured cache service for application-level caching
    builder.Services.AddScoped<ICacheService, MemoryCacheService>();
}
else
{
    builder.Services.AddSingleton<IConnectionMultiplexer>(
        _ => ConnectionMultiplexer.Connect(builder.Configuration["Redis:ConnectionString"] ?? "localhost:6379"));

    // Register cache services
    builder.Services.AddSingleton<RedisCacheService>();
    builder.Services.AddSingleton<IRedisCacheService>(provider => provider.GetRequiredService<RedisCacheService>());
    // Add structured cache service for application-level caching (fallback to memory cache in production too)
    builder.Services.AddMemoryCache();
    builder.Services.AddScoped<ICacheService, MemoryCacheService>();
}

// Register business services
if (builder.Environment.IsDevelopment())
{
    // Use real services with Supabase database, but mock external price providers
    builder.Services.AddScoped<WatchlistService>();
    builder.Services.AddScoped<IWatchlistService>(provider => provider.GetRequiredService<WatchlistService>());
    builder.Services.AddScoped<BulkPriceService>();
    builder.Services.AddScoped<IBulkPriceService>(provider => provider.GetRequiredService<BulkPriceService>());

    // Mock price service for development (since we don't have real API keys)
    builder.Services.AddScoped<IPriceService, MockPriceService>();
    builder.Services.AddScoped<IHistoricalPriceRegistry, MockHistoricalPriceRegistry>();
}
else
{
    // Production services
    builder.Services.AddScoped<PriceService>();
    builder.Services.AddScoped<IPriceService>(provider => provider.GetRequiredService<PriceService>());
    builder.Services.AddScoped<WatchlistService>();
    builder.Services.AddScoped<IWatchlistService>(provider => provider.GetRequiredService<WatchlistService>());
    builder.Services.AddScoped<BulkPriceService>();
    builder.Services.AddScoped<IBulkPriceService>(provider => provider.GetRequiredService<BulkPriceService>());
}

// Add memory cache for bulk price operations
builder.Services.AddMemoryCache();

// Add response caching
builder.Services.AddResponseCaching();

// Register external data providers
builder.Services.AddHttpClient<FinnhubProvider>();
builder.Services.AddHttpClient<PolygonProvider>();

// Register FinnhubProvider for all interfaces
builder.Services.AddSingleton<IPriceProvider, FinnhubProvider>();
builder.Services.AddSingleton<IHistoricalPriceProvider, FinnhubProvider>();
builder.Services.AddSingleton<IBrokerMetadataProvider, FinnhubProvider>();
builder.Services.AddSingleton<ISymbolProvider, FinnhubProvider>();

// Register PolygonProvider for all interfaces
builder.Services.AddSingleton<IPriceProvider, PolygonProvider>();
builder.Services.AddSingleton<IHistoricalPriceProvider, PolygonProvider>();
builder.Services.AddSingleton<IBrokerMetadataProvider, PolygonProvider>();
builder.Services.AddSingleton<ISymbolProvider, PolygonProvider>();

// Register registries
builder.Services.AddSingleton<BrokerRegistry>();
builder.Services.AddSingleton<IBrokerRegistry>(provider => provider.GetRequiredService<BrokerRegistry>());
builder.Services.AddSingleton<HistoricalPriceRegistry>();
builder.Services.AddSingleton<IHistoricalPriceRegistry>(provider => provider.GetRequiredService<HistoricalPriceRegistry>());
builder.Services.AddSingleton<BrokerMetadataRegistry>();
builder.Services.AddSingleton<IBrokerMetadataRegistry>(provider => provider.GetRequiredService<BrokerMetadataRegistry>());
builder.Services.AddSingleton<SymbolRegistry>();
builder.Services.AddSingleton<ISymbolRegistry>(provider => provider.GetRequiredService<SymbolRegistry>());

// Auth (Supabase JWT) - Load from environment variables
// Environment variables automatically override appsettings.json values
var supabaseUrl = builder.Configuration["SUPABASE_URL"];
var supabaseJwtSecret = builder.Configuration["SUPABASE_JWT_SECRET"];

if (string.IsNullOrEmpty(supabaseUrl))
{
    throw new InvalidOperationException("SUPABASE_URL must be configured");
}

if (string.IsNullOrEmpty(supabaseJwtSecret))
{
    throw new InvalidOperationException("SUPABASE_JWT_SECRET must be configured");
}

JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        // Use JWT secret for Supabase token validation (same as auth-service)
        var key = Encoding.UTF8.GetBytes(supabaseJwtSecret);

        options.RequireHttpsMetadata = !builder.Environment.IsDevelopment();
        options.SaveToken = true;

        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = $"{supabaseUrl}/auth/v1",
            ValidateAudience = false,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(key),
            ClockSkew = TimeSpan.FromMinutes(5),
            RequireExpirationTime = true,
            RequireSignedTokens = true
        };

        // Add event handlers for debugging
        options.Events = new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                logger.LogWarning("JWT Authentication failed in MarketData service: {Error}", context.Exception.Message);
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                var userId = context.Principal?.FindFirst("sub")?.Value;
                logger.LogInformation("JWT Token validated in MarketData service for user: {UserId}", userId);
                return Task.CompletedTask;
            }
        };
    });

var app = builder.Build();

// Test database connection during startup (skip in testing environment)
if (!app.Environment.IsEnvironment("Testing"))
{
    await TestDatabaseConnection(app.Services);
}

// Skip database creation since we're using existing Supabase tables

// Configure middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Market Data Service API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

app.UseCors("AllowAll");
app.UseResponseCaching();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

// Map health check endpoint
app.MapHealthChecks("/health");

// Add error handling endpoint
app.Map("/error", (ILogger<Program> logger) =>
{
    logger.LogError("An unhandled exception occurred in MarketData service");
    return Results.Problem("An error occurred while processing your request.");
});

// Add a root endpoint for API information
app.MapGet("/", () => new
{
    service = "Market Data Service",
    version = "1.0.0",
    endpoints = new
    {
        prices = new[]
        {
            "GET /api/prices/{symbol} - Get current price for a symbol",
            "GET /api/prices/bulk - Get bulk prices for multiple symbols"
        },
        historical = new[]
        {
            "GET /api/historical/{symbol} - Get historical data for a symbol"
        },
        watchlist = new[]
        {
            "GET /api/watchlist - Get user's watchlist",
            "POST /api/watchlist - Add symbol to watchlist",
            "DELETE /api/watchlist/{symbol} - Remove symbol from watchlist"
        },
        brokers = new[]
        {
            "GET /api/brokers - Get available brokers",
            "GET /api/brokers/{brokerId}/metadata - Get broker metadata"
        },
        symbols = new[]
        {
            "GET /api/symbols - Get all symbols from all brokers",
            "GET /api/symbols/market/{marketType} - Get symbols by market type (stocks, forex, crypto)",
            "GET /api/symbols/active - Get most active/traded symbols",
            "GET /api/symbols/search - Search symbols by query",
            "GET /api/symbols/brokers - Get available brokers for symbol fetching",
            "POST /api/symbols/search - Bulk symbol search",
            "POST /api/symbols/active - Bulk get most active symbols"
        },
        health = new[]
        {
            "GET /health - Health check"
        }
    }
});

app.Run();



// Test database connection during startup
static async Task TestDatabaseConnection(IServiceProvider services)
{
    using var scope = services.CreateScope();
    var context = scope.ServiceProvider.GetRequiredService<MarketDataContext>();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

    try
    {
        logger.LogInformation("Testing database connection...");

        // Test basic connectivity
        var canConnect = await context.Database.CanConnectAsync();
        if (canConnect)
        {
            logger.LogInformation("✅ Database connection successful!");

            // Test if tables exist with timeout protection
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));

                logger.LogInformation("Testing watchlists table access...");
                var watchlistCount = await context.Watchlists.CountAsync(cts.Token);
                logger.LogInformation("✅ Watchlists table accessible: {WatchlistCount} records", watchlistCount);

                logger.LogInformation("Testing watchlist_symbols table access...");
                var symbolCount = await context.WatchlistItems.CountAsync(cts.Token);
                logger.LogInformation("✅ WatchlistItems table accessible: {SymbolCount} records", symbolCount);

                logger.LogInformation("✅ All tables accessible - Watchlists: {WatchlistCount}, Symbols: {SymbolCount}",
                    watchlistCount, symbolCount);
            }
            catch (OperationCanceledException)
            {
                logger.LogWarning("⚠️ Table access timed out - tables may not exist or network is slow");
                logger.LogInformation("Service will continue but database operations may fail.");
            }
            catch (Exception ex)
            {
                logger.LogWarning("⚠️ Tables may not exist yet: {Error}", ex.Message);
                logger.LogInformation("This is normal if tables haven't been created in Supabase yet.");

                // Try to check if it's a table name issue
                if (ex.Message.Contains("relation") && ex.Message.Contains("does not exist"))
                {
                    logger.LogInformation("💡 Hint: Make sure the tables 'watchlists' and 'watchlist_symbols' exist in your Supabase database");
                }
            }
        }
        else
        {
            logger.LogError("❌ Cannot connect to database");
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "❌ Database connection test failed: {Error}", ex.Message);
        logger.LogWarning("Service will continue but database operations may fail.");
    }
}

// Make the implicit Program class public for testing
public partial class Program { }
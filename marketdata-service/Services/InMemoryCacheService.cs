using MarketDataService.Interfaces;
using Microsoft.Extensions.Caching.Memory;

namespace MarketDataService.Services;

public class InMemoryCacheService : IRedisCacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<InMemoryCacheService> _logger;

    public InMemoryCacheService(IMemoryCache memoryCache, ILogger<InMemoryCacheService> logger)
    {
        _memoryCache = memoryCache;
        _logger = logger;
    }

    public Task<T?> GetAsync<T>(string key) where T : class
    {
        _logger.LogDebug("Getting cache key: {Key}", key);

        if (_memoryCache.TryGetValue(key, out var value) && value is T typedValue)
        {
            _logger.LogDebug("Cache hit for key: {Key}", key);
            return Task.FromResult<T?>(typedValue);
        }

        _logger.LogDebug("Cache miss for key: {Key}", key);
        return Task.FromResult<T?>(default(T));
    }

    public Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class
    {
        _logger.LogDebug("Setting cache key: {Key} with expiry: {Expiry}", key, expiry);

        var options = new MemoryCacheEntryOptions();
        if (expiry.HasValue)
        {
            options.AbsoluteExpirationRelativeToNow = expiry.Value;
        }
        else
        {
            // Default expiry of 5 minutes for development
            options.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);
        }

        _memoryCache.Set(key, value, options);
        return Task.CompletedTask;
    }

    public Task RemoveAsync(string key)
    {
        _logger.LogDebug("Removing cache key: {Key}", key);
        _memoryCache.Remove(key);
        return Task.CompletedTask;
    }

    public Task<bool> ExistsAsync(string key)
    {
        var exists = _memoryCache.TryGetValue(key, out _);
        _logger.LogDebug("Cache key {Key} exists: {Exists}", key, exists);
        return Task.FromResult(exists);
    }
}

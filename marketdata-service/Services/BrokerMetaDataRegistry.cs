using MarketDataService.Interfaces;
using MarketDataService.Models;

namespace MarketDataService.Services;

public class BrokerMetadataRegistry : IBrokerMetadataRegistry
{
    private readonly List<IBrokerMetadataProvider> _providers;

    public BrokerMetadataRegistry(IEnumerable<IBrokerMetadataProvider> providers)
    {
        _providers = providers.ToList();
    }

    public IEnumerable<BrokerMetadata> ListAll() =>
        _providers.Select(p => p.GetMetadata());
}
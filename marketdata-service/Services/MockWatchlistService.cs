using MarketDataService.Interfaces;
using MarketDataService.Models;

namespace MarketDataService.Services;

public class MockWatchlistService : IWatchlistService
{
    private readonly ILogger<MockWatchlistService> _logger;
    private static readonly Dictionary<string, List<WatchlistDto>> _userWatchlists = new();
    private static int _nextId = 1;

    public MockWatchlistService(ILogger<MockWatchlistService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> TestDatabaseConnectionAsync()
    {
        _logger.LogInformation("Mock: Testing database connection");
        await Task.Delay(10); // Simulate connection test
        return true; // Mock always returns success
    }

    public async Task<List<WatchlistDto>> GetUserWatchlistsAsync(Guid userId)
    {
        var userIdString = userId.ToString();
        _logger.LogInformation("Mock: Getting watchlists for user {UserId}", userId);

        await Task.Delay(50); // Simulate database call

        if (!_userWatchlists.ContainsKey(userIdString))
        {
            // Create a default watchlist for new users
            _userWatchlists[userIdString] = new List<WatchlistDto>
            {
                new WatchlistDto
                {
                    Id = _nextId++,
                    Name = "My Watchlist",
                    IsGlobal = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    ItemCount = 2,
                    Items = new List<WatchlistItemWithPriceDto>
                    {
                        new WatchlistItemWithPriceDto
                        {
                            Id = 1,
                            Symbol = "AAPL",
                            AddedAt = DateTime.UtcNow,
                            SortOrder = 1
                        },
                        new WatchlistItemWithPriceDto
                        {
                            Id = 2,
                            Symbol = "GOOGL",
                            AddedAt = DateTime.UtcNow,
                            SortOrder = 2
                        }
                    }
                }
            };
        }
        
        return _userWatchlists[userIdString];
    }

    public async Task<WatchlistDto?> GetWatchlistAsync(Guid userId, long watchlistId, bool includePrices = false)
    {
        _logger.LogInformation("Mock: Getting watchlist {WatchlistId} for user {UserId}", watchlistId, userId);

        await Task.Delay(50);

        var watchlists = await GetUserWatchlistsAsync(userId);
        return watchlists.FirstOrDefault(w => w.Id == watchlistId);
    }

    public async Task<WatchlistDto> CreateWatchlistAsync(Guid userId, CreateWatchlistRequest request)
    {
        _logger.LogInformation("Mock: Creating watchlist '{Name}' for user {UserId}", request.Name, userId);
        
        await Task.Delay(50);
        
        var userIdString = userId.ToString();
        if (!_userWatchlists.ContainsKey(userIdString))
        {
            _userWatchlists[userIdString] = new List<WatchlistDto>();
        }

        var newWatchlist = new WatchlistDto
        {
            Id = _nextId++,
            Name = request.Name,
            IsGlobal = request.IsGlobal,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            ItemCount = 0,
            Items = new List<WatchlistItemWithPriceDto>()
        };

        _userWatchlists[userIdString].Add(newWatchlist);
        return newWatchlist;
    }

    public async Task<WatchlistDto?> UpdateWatchlistAsync(Guid userId, long watchlistId, UpdateWatchlistRequest request)
    {
        _logger.LogInformation("Mock: Updating watchlist {WatchlistId} for user {UserId}", watchlistId, userId);
        
        await Task.Delay(50);
        
        var watchlist = await GetWatchlistAsync(userId, watchlistId);
        if (watchlist == null) return null;

        watchlist.Name = request.Name ?? watchlist.Name;
        if (request.IsGlobal.HasValue)
        {
            watchlist.IsGlobal = request.IsGlobal.Value;
        }
        watchlist.UpdatedAt = DateTime.UtcNow;

        return watchlist;
    }

    public async Task<bool> DeleteWatchlistAsync(Guid userId, long watchlistId)
    {
        _logger.LogInformation("Mock: Deleting watchlist {WatchlistId} for user {UserId}", watchlistId, userId);
        
        await Task.Delay(50);
        
        var userIdString = userId.ToString();
        if (!_userWatchlists.ContainsKey(userIdString)) return false;

        var watchlist = _userWatchlists[userIdString].FirstOrDefault(w => w.Id == watchlistId);
        if (watchlist == null) return false;

        _userWatchlists[userIdString].Remove(watchlist);
        return true;
    }

    public async Task<WatchlistDto?> AddSymbolsAsync(Guid userId, long watchlistId, AddSymbolsRequest request)
    {
        _logger.LogInformation("Mock: Adding symbols to watchlist {WatchlistId} for user {UserId}", watchlistId, userId);
        
        await Task.Delay(50);
        
        var watchlist = await GetWatchlistAsync(userId, watchlistId);
        if (watchlist == null) return null;

        var nextOrder = watchlist.Items.Any() ? watchlist.Items.Max(i => i.SortOrder) + 1 : 1;
        var nextId = watchlist.Items.Any() ? watchlist.Items.Max(i => i.Id) + 1 : 1;

        foreach (var symbol in request.Symbols)
        {
            if (!watchlist.Items.Any(i => i.Symbol.Equals(symbol, StringComparison.OrdinalIgnoreCase)))
            {
                watchlist.Items.Add(new WatchlistItemWithPriceDto
                {
                    Id = nextId++,
                    Symbol = symbol.ToUpper(),
                    AddedAt = DateTime.UtcNow,
                    SortOrder = nextOrder++
                });
            }
        }

        watchlist.UpdatedAt = DateTime.UtcNow;
        return watchlist;
    }

    public async Task<bool> RemoveSymbolAsync(Guid userId, long watchlistId, string symbol)
    {
        _logger.LogInformation("Mock: Removing symbol {Symbol} from watchlist {WatchlistId} for user {UserId}", symbol, watchlistId, userId);
        
        await Task.Delay(50);
        
        var watchlist = await GetWatchlistAsync(userId, watchlistId);
        if (watchlist == null) return false;

        var item = watchlist.Items.FirstOrDefault(i => i.Symbol.Equals(symbol, StringComparison.OrdinalIgnoreCase));
        if (item == null) return false;

        watchlist.Items.Remove(item);
        watchlist.UpdatedAt = DateTime.UtcNow;
        return true;
    }

    public async Task<WatchlistDto?> ReorderItemsAsync(Guid userId, long watchlistId, ReorderItemsRequest request)
    {
        _logger.LogInformation("Mock: Reordering items in watchlist {WatchlistId} for user {UserId}", watchlistId, userId);
        
        await Task.Delay(50);
        
        var watchlist = await GetWatchlistAsync(userId, watchlistId);
        if (watchlist == null) return null;

        // Update the order of items based on the request
        foreach (var item in request.Items)
        {
            var watchlistItem = watchlist.Items.FirstOrDefault(i => i.Id == item.Id);
            if (watchlistItem != null)
            {
                watchlistItem.SortOrder = item.SortOrder;
            }
        }

        // Sort items by order
        watchlist.Items = watchlist.Items.OrderBy(i => i.SortOrder).ToList();
        watchlist.UpdatedAt = DateTime.UtcNow;
        
        return watchlist;
    }

    public async Task<WatchlistDto?> GetGlobalWatchlistAsync(Guid userId, bool includePrices = false)
    {
        _logger.LogInformation("Mock: Getting global watchlist for user {UserId}", userId);

        await Task.Delay(50);

        var watchlists = await GetUserWatchlistsAsync(userId);

        // Return the first watchlist as the global (or null if no watchlists)
        return watchlists.FirstOrDefault();
    }
}

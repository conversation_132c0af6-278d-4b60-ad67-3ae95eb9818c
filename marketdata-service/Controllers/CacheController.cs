using MarketDataService.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace MarketDataService.Controllers;

[ApiController]
[Route("api/cache")]
[Authorize] // Only authenticated users can access cache stats
public class CacheController : ControllerBase
{
    private readonly EnhancedCacheService _cacheService;
    private readonly ILogger<CacheController> _logger;

    public CacheController(EnhancedCacheService cacheService, ILogger<CacheController> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    [HttpGet("stats")]
    public IActionResult GetStatistics()
    {
        try
        {
            var stats = _cacheService.GetStatistics();
            return Ok(new
            {
                timestamp = DateTime.UtcNow,
                statistics = stats.Select(kvp => new
                {
                    key = kvp.Key,
                    requestCount = kvp.Value.RequestCount,
                    l1HitCount = kvp.Value.L1HitCount,
                    l2HitCount = kvp.Value.L2HitCount,
                    missCount = kvp.Value.MissCount,
                    l1HitRatio = Math.Round(kvp.Value.L1HitRatio * 100, 2),
                    l2HitRatio = Math.Round(kvp.Value.L2HitRatio * 100, 2),
                    overallHitRatio = Math.Round(kvp.Value.OverallHitRatio * 100, 2),
                    missRatio = Math.Round(kvp.Value.MissRatio * 100, 2)
                }).OrderByDescending(x => x.requestCount)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cache statistics");
            return Problem("Failed to retrieve cache statistics");
        }
    }

    [HttpPost("clear-stats")]
    public IActionResult ClearStatistics()
    {
        try
        {
            _cacheService.ClearStatistics();
            _logger.LogInformation("Cache statistics cleared by user");
            return Ok(new { message = "Cache statistics cleared successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache statistics");
            return Problem("Failed to clear cache statistics");
        }
    }

    [HttpPost("warmup")]
    public async Task<IActionResult> WarmupCache()
    {
        try
        {
            // Define common cache keys to warm up
            var warmupTasks = new Dictionary<string, Func<Task<object>>>
            {
                // Add your most commonly accessed data here
                // Example: popular stock symbols, user watchlists, etc.
            };

            await _cacheService.WarmupAsync(warmupTasks);
            _logger.LogInformation("Cache warmup initiated by user");
            return Ok(new { message = "Cache warmup completed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache warmup");
            return Problem("Failed to warm up cache");
        }
    }
}
using System.Net;
using System.Text;
using System.Text.Json;
using AssistantService.Controllers;
using AssistantService.Models;
using AssistantService.Services;
using AssistantService.Configuration;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace AssistantService.Tests.Controllers;

public class AssistantControllerTests
{
    private readonly Mock<IOllamaClient> _ollamaClientMock;
    private readonly Mock<IPromptInjectionDetector> _injectionDetectorMock;
    private readonly Mock<IContentFilter> _contentFilterMock;
    private readonly Mock<IOptions<SecurityOptions>> _securityOptionsMock;
    private readonly AssistantController _controller;

    public AssistantControllerTests()
    {
        _ollamaClientMock = new Mock<IOllamaClient>();
        _injectionDetectorMock = new Mock<IPromptInjectionDetector>();
        _contentFilterMock = new Mock<IContentFilter>();
        _securityOptionsMock = new Mock<IOptions<SecurityOptions>>();

        // Setup security options with all features enabled for testing
        _securityOptionsMock.Setup(x => x.Value).Returns(new SecurityOptions
        {
            EnablePromptInjectionDetection = true,
            EnableContentFiltering = true,
            EnableRateLimiting = true,
            MaxInputLength = 4000
        });

        _controller = new AssistantController(_ollamaClientMock.Object, _injectionDetectorMock.Object, _contentFilterMock.Object, _securityOptionsMock.Object);

        // Setup default safe responses for security services
        _injectionDetectorMock.Setup(x => x.AnalyzeInput(It.IsAny<string>()))
            .Returns((string input) => new PromptInjectionResult
            {
                IsInjectionDetected = false,
                RiskLevel = RiskLevel.Low,
                SanitizedInput = input
            });

        _contentFilterMock.Setup(x => x.FilterContent(It.IsAny<string>()))
            .Returns((string input) => new ContentFilterResult
            {
                IsContentSafe = true,
                FilteredContent = input,
                RiskLevel = ContentRiskLevel.Safe
            });
    }

    [Fact]
    public async Task Ask_WithValidRequest_ReturnsOkWithResponse()
    {
        // Arrange
        const string prompt = "What is the weather like?";
        const string expectedReply = "I don't have access to real-time weather data.";
        
        var request = new AskRequest { Prompt = prompt };
        
        _ollamaClientMock
            .Setup(x => x.AskAsync(prompt))
            .ReturnsAsync(expectedReply);

        // Act
        var result = await _controller.Ask(request);

        // Assert
        result.Should().BeOfType<ActionResult<AskResponse>>();
        
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var response = okResult.Value.Should().BeOfType<AskResponse>().Subject;
        
        response.Reply.Should().Be(expectedReply);
    }

    [Fact]
    public async Task Ask_WithEmptyPrompt_ReturnsBadRequest()
    {
        // Arrange
        const string emptyPrompt = "";
        var request = new AskRequest { Prompt = emptyPrompt };

        // Act
        var result = await _controller.Ask(request);

        // Assert
        result.Should().BeOfType<ActionResult<AskResponse>>();

        var badRequestResult = result.Result.Should().BeOfType<BadRequestObjectResult>().Subject;
        badRequestResult.Value.Should().Be("Prompt cannot be empty");

        _ollamaClientMock.Verify(x => x.AskAsync(It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task Ask_WhenOllamaClientThrows_ReturnsInternalServerError()
    {
        // Arrange
        const string prompt = "Test prompt";
        var request = new AskRequest { Prompt = prompt };

        _ollamaClientMock
            .Setup(x => x.AskAsync(prompt))
            .ThrowsAsync(new InvalidOperationException("AI service is temporarily unavailable"));

        // Act
        var result = await _controller.Ask(request);

        // Assert
        result.Result.Should().BeOfType<ObjectResult>();
        var objectResult = result.Result as ObjectResult;
        objectResult!.StatusCode.Should().Be(503);
        objectResult.Value.Should().Be("AI service is temporarily unavailable");
    }

    [Fact]
    public async Task Ask_WhenGenericExceptionThrown_ReturnsInternalServerError()
    {
        // Arrange
        const string prompt = "Test prompt";
        var request = new AskRequest { Prompt = prompt };

        _ollamaClientMock
            .Setup(x => x.AskAsync(prompt))
            .ThrowsAsync(new Exception("Some unexpected error"));

        // Act
        var result = await _controller.Ask(request);

        // Assert
        result.Result.Should().BeOfType<ObjectResult>();
        var objectResult = result.Result as ObjectResult;
        objectResult!.StatusCode.Should().Be(500);
        objectResult.Value.Should().Be("Something went wrong. Please try again.");
    }

    [Fact]
    public async Task Ask_CallsOllamaClientWithCorrectPrompt()
    {
        // Arrange
        const string prompt = "Explain quantum computing";
        const string expectedReply = "Quantum computing explanation...";
        
        var request = new AskRequest { Prompt = prompt };
        
        _ollamaClientMock
            .Setup(x => x.AskAsync(prompt))
            .ReturnsAsync(expectedReply);

        // Act
        await _controller.Ask(request);

        // Assert
        _ollamaClientMock.Verify(x => x.AskAsync(prompt), Times.Once);
    }

    [Theory]
    [InlineData("Hello")]
    [InlineData("What is AI?")]
    [InlineData("Tell me a joke")]
    [InlineData("How does machine learning work?")]
    public async Task Ask_WithVariousPrompts_ReturnsExpectedStructure(string prompt)
    {
        // Arrange
        const string mockReply = "Mock response";
        var request = new AskRequest { Prompt = prompt };
        
        _ollamaClientMock
            .Setup(x => x.AskAsync(It.IsAny<string>()))
            .ReturnsAsync(mockReply);

        // Act
        var result = await _controller.Ask(request);

        // Assert
        result.Should().BeOfType<ActionResult<AskResponse>>();
        
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var response = okResult.Value.Should().BeOfType<AskResponse>().Subject;
        
        response.Reply.Should().Be(mockReply);
    }
}

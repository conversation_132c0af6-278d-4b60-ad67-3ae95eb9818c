using System.Net;
using System.Text;
using System.Text.Json;
using AssistantService.Services;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Moq;
using Moq.Protected;
using Xunit;

namespace AssistantService.Tests.Services;

public class OllamaClientTests
{
    private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;
    private readonly HttpClient _httpClient;
    private readonly Mock<IConfiguration> _configurationMock;
    private readonly Mock<ICacheService> _cacheServiceMock;
    private readonly OllamaClient _ollamaClient;

    public OllamaClientTests()
    {
        _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_httpMessageHandlerMock.Object);
        _configurationMock = new Mock<IConfiguration>();
        _cacheServiceMock = new Mock<ICacheService>();

        // Setup default configuration
        _configurationMock.Setup(x => x["OLLAMA_URL"]).Returns("http://ollama:11434/api/chat");

        // Setup cache service to always call the factory method (simulate cache miss)
        _cacheServiceMock.Setup(x => x.GetOrSetAsync(It.IsAny<string>(), It.IsAny<Func<Task<object>>>(), It.IsAny<TimeSpan?>()))
            .Returns<string, Func<Task<object>>, TimeSpan?>(async (key, factory, expiration) => await factory());

        _ollamaClient = new OllamaClient(_httpClient, _configurationMock.Object, _cacheServiceMock.Object);
    }

    [Fact]
    public async Task AskAsync_WithValidPrompt_ReturnsExpectedResponse()
    {
        // Arrange
        const string prompt = "Hello, how are you?";
        const string expectedResponse = "I'm doing well, thank you!";

        var ollamaResponse = new {
            message = new { role = "assistant", content = expectedResponse },
            done = true
        };
        var responseContent = JsonSerializer.Serialize(ollamaResponse);



        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            });

        // Act
        var result = await _ollamaClient.AskAsync(prompt);

        // Assert
        result.Should().Be(expectedResponse);
    }

    [Fact]
    public async Task AskAsync_WithEmptyResponse_ReturnsNoReplyMessage()
    {
        // Arrange
        const string prompt = "Test prompt";

        var ollamaResponse = new {
            message = new { role = "assistant", content = "" },
            done = true
        };
        var responseContent = JsonSerializer.Serialize(ollamaResponse);

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            });

        // Act
        var result = await _ollamaClient.AskAsync(prompt);

        // Assert
        result.Should().Be("Sorry, I couldn't generate a response.");
    }

    [Fact]
    public async Task AskAsync_WithNullResponse_ReturnsNoReplyMessage()
    {
        // Arrange
        const string prompt = "Test prompt";

        var responseContent = "{}"; // No message property

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            });

        // Act
        var result = await _ollamaClient.AskAsync(prompt);

        // Assert
        result.Should().Be("Sorry, I couldn't generate a response.");
    }

    [Fact]
    public async Task AskAsync_WithHttpError_ThrowsHttpRequestException()
    {
        // Arrange
        const string prompt = "Test prompt";



        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Server Error")
            });

        // Act & Assert
        await _ollamaClient.Invoking(x => x.AskAsync(prompt))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Unable to connect to AI service");
    }

    [Fact]
    public async Task AskAsync_SendsCorrectRequestToOllama()
    {
        // Arrange
        const string prompt = "Test prompt";
        const string expectedResponse = "Test response";

        var ollamaResponse = new {
            message = new { role = "assistant", content = expectedResponse },
            done = true
        };
        var responseContent = JsonSerializer.Serialize(ollamaResponse);

        HttpRequestMessage? capturedRequest = null;



        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .Callback<HttpRequestMessage, CancellationToken>((request, _) => capturedRequest = request)
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent, Encoding.UTF8, "application/json")
            });

        // Create a test client without caching to avoid cache service issues
        var testHttpClient = new HttpClient(_httpMessageHandlerMock.Object);
        var testConfig = new Mock<IConfiguration>();
        testConfig.Setup(x => x["OLLAMA_URL"]).Returns("http://ollama:11434/api/chat");

        // Create a simple cache service that always calls the factory
        var simpleCacheService = new Mock<ICacheService>();
        simpleCacheService.Setup(x => x.GetOrSetAsync(It.IsAny<string>(), It.IsAny<Func<Task<object?>>>(), It.IsAny<TimeSpan?>()))
            .Returns<string, Func<Task<object?>>, TimeSpan?>(async (key, factory, expiration) => await factory());

        var testOllamaClient = new OllamaClient(testHttpClient, testConfig.Object, simpleCacheService.Object);

        // Act
        await testOllamaClient.AskAsync(prompt);

        // Assert
        capturedRequest.Should().NotBeNull();
        capturedRequest!.Method.Should().Be(HttpMethod.Post);
        capturedRequest.RequestUri.Should().Be("http://ollama:11434/api/chat");

        var requestContent = await capturedRequest.Content!.ReadAsStringAsync();
        var requestPayload = JsonSerializer.Deserialize<JsonElement>(requestContent);

        requestPayload.GetProperty("model").GetString().Should().Be("mistral");
        requestPayload.GetProperty("stream").GetBoolean().Should().Be(false);

        var messages = requestPayload.GetProperty("messages");
        messages.GetArrayLength().Should().Be(1);

        var message = messages[0];
        message.GetProperty("role").GetString().Should().Be("user");
        message.GetProperty("content").GetString().Should().Be(prompt);
    }
}

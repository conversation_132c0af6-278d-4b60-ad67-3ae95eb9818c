using System.Net;
using System.Text;
using auth_service.Services;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Moq;
using Moq.Protected;
using Xunit;

namespace AuthService.Tests.Services;

public class SupabaseClientTests
{
    private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;
    private readonly HttpClient _httpClient;
    private readonly Mock<IHttpClientFactory> _httpClientFactoryMock;
    private readonly Mock<IConfiguration> _configurationMock;
    private readonly Mock<ICacheService> _cacheServiceMock;
    private readonly SupabaseClient _supabaseClient;

    public SupabaseClientTests()
    {
        _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_httpMessageHandlerMock.Object);
        _httpClientFactoryMock = new Mock<IHttpClientFactory>();
        _configurationMock = new Mock<IConfiguration>();
        _cacheServiceMock = new Mock<ICacheService>();

        // Setup configuration
        _configurationMock.Setup(x => x["SUPABASE_URL"]).Returns("https://test.supabase.co");
        _configurationMock.Setup(x => x["SUPABASE_SERVICE_ROLE_KEY"]).Returns("test-service-role-key");
        _configurationMock.Setup(x => x["SUPABASE_ANON_KEY"]).Returns("test-anon-key");

        // Setup HttpClientFactory to return our mocked HttpClient
        _httpClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(_httpClient);

        _supabaseClient = new SupabaseClient(_httpClient, _httpClientFactoryMock.Object, _cacheServiceMock.Object, _configurationMock.Object);
    }

    [Fact]
    public async Task GetProfileAsync_WithValidUserId_ReturnsSupabaseUser()
    {
        // Arrange
        const string userId = "test-user-id";
        const string expectedResponse = """
        {
            "id": "test-user-id",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "email_confirmed_at": "2023-01-01T00:00:00Z",
            "created_at": "2023-01-01T00:00:00Z",
            "user_metadata": {
                "full_name": "Test User",
                "username": "testuser",
                "broker_id": "broker123"
            }
        }
        """;

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.Method == HttpMethod.Get &&
                    req.RequestUri!.ToString().Contains($"/auth/v1/admin/users/{userId}") &&
                    req.Headers.Contains("apikey") &&
                    req.Headers.Contains("Authorization")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(expectedResponse, Encoding.UTF8, "application/json")
            });

        // Act
        var result = await _supabaseClient.GetProfileAsync(userId);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(userId);
        result.Email.Should().Be("<EMAIL>");
        result.UserMetadata.Should().ContainKey("full_name");
        result.UserMetadata!["full_name"].ToString().Should().Be("Test User");
    }

    [Fact]
    public async Task GetProfileAsync_WithEmptyUserId_ThrowsArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _supabaseClient.GetProfileAsync(""));
        await Assert.ThrowsAsync<ArgumentException>(() => _supabaseClient.GetProfileAsync(null!));
    }

    [Fact]
    public async Task GetProfileAsync_WhenHttpRequestFails_ThrowsHttpRequestException()
    {
        // Arrange
        const string userId = "test-user-id";

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Content = new StringContent("Internal Server Error")
            });

        // Act & Assert
        await Assert.ThrowsAsync<HttpRequestException>(() => _supabaseClient.GetProfileAsync(userId));
    }

    [Fact]
    public async Task GetProfileAsync_WhenNoProfileFound_ReturnsNull()
    {
        // Arrange
        const string userId = "non-existent-user";

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.Method == HttpMethod.Get &&
                    req.RequestUri!.ToString().Contains($"/auth/v1/admin/users/{userId}")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("{\"message\":\"User not found\"}", Encoding.UTF8, "application/json")
            });

        // Act
        var result = await _supabaseClient.GetProfileAsync(userId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetProfileAsync_SetsCorrectHeaders()
    {
        // Arrange
        const string userId = "test-user-id";
        const string expectedResponse = """
        {
            "id": "test-user-id",
            "email": "<EMAIL>",
            "created_at": "2023-01-01T00:00:00Z",
            "user_metadata": {}
        }
        """;
        HttpRequestMessage? capturedRequest = null;

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .Callback<HttpRequestMessage, CancellationToken>((req, _) => capturedRequest = req)
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(expectedResponse, Encoding.UTF8, "application/json")
            });

        // Act
        await _supabaseClient.GetProfileAsync(userId);

        // Assert
        capturedRequest.Should().NotBeNull();
        capturedRequest!.Headers.Should().ContainKey("apikey");
        capturedRequest.Headers.Should().ContainKey("Authorization");
        capturedRequest.Headers.GetValues("apikey").First().Should().Be("test-service-role-key");
        capturedRequest.Headers.GetValues("Authorization").First().Should().Be("Bearer test-service-role-key");
    }

    [Fact]
    public async Task GetProfileAsync_BuildsCorrectUrl()
    {
        // Arrange
        const string userId = "test-user-id";
        const string expectedResponse = """
        {
            "id": "test-user-id",
            "email": "<EMAIL>",
            "created_at": "2023-01-01T00:00:00Z",
            "user_metadata": {}
        }
        """;
        HttpRequestMessage? capturedRequest = null;

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .Callback<HttpRequestMessage, CancellationToken>((req, _) => capturedRequest = req)
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(expectedResponse, Encoding.UTF8, "application/json")
            });

        // Act
        await _supabaseClient.GetProfileAsync(userId);

        // Assert
        capturedRequest.Should().NotBeNull();
        capturedRequest!.RequestUri!.ToString().Should().Contain("https://test.supabase.co/auth/v1/admin/users");
        capturedRequest.RequestUri.ToString().Should().Contain(userId);
        capturedRequest.RequestUri.ToString().Should().Contain(userId);
    }
}

using auth_service.Models;
using auth_service.Services;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Moq;
using Moq.Protected;
using System.Net;
using System.Text;
using System.Text.Json;

namespace AuthService.Tests.Services;

public class SupabaseClientGoogleSignInTests
{
    private readonly SupabaseClient _supabaseClient;
    private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;
    private readonly HttpClient _httpClient;
    private readonly Mock<IHttpClientFactory> _httpClientFactoryMock;
    private readonly Mock<ICacheService> _cacheServiceMock;

    public SupabaseClientGoogleSignInTests()
    {
        _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_httpMessageHandlerMock.Object);
        _httpClientFactoryMock = new Mock<IHttpClientFactory>();
        _cacheServiceMock = new Mock<ICacheService>();

        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["SUPABASE_URL"] = "https://test.supabase.co",
                ["SUPABASE_SERVICE_ROLE_KEY"] = "test-service-role-key",
                ["SUPABASE_ANON_KEY"] = "test-anon-key"
            })
            .Build();

        // Setup HttpClientFactory to return our mocked HttpClient
        _httpClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(_httpClient);

        _supabaseClient = new SupabaseClient(_httpClient, _httpClientFactoryMock.Object, _cacheServiceMock.Object, configuration);
    }

    [Fact]
    public async Task GetGoogleSignInUrlAsync_WithoutRedirectTo_ReturnsValidOAuthResponse()
    {
        // Act
        var result = await _supabaseClient.GetGoogleSignInUrlAsync();

        // Assert
        result.Should().NotBeNull();
        result.Provider.Should().Be("google");
        result.Url.Should().Contain("provider=google");
        result.Url.Should().Contain("https://test.supabase.co/auth/v1/authorize");
    }

    [Fact]
    public async Task GetGoogleSignInUrlAsync_WithRedirectTo_ReturnsValidOAuthResponseWithRedirect()
    {
        // Arrange
        var redirectTo = "https://example.com/callback";

        // Act
        var result = await _supabaseClient.GetGoogleSignInUrlAsync(redirectTo);

        // Assert
        result.Should().NotBeNull();
        result.Provider.Should().Be("google");
        result.Url.Should().Contain("provider=google");
        result.Url.Should().Contain($"redirect_to={Uri.EscapeDataString(redirectTo)}");
    }

    [Fact]
    public async Task SignInWithGoogleAsync_WithValidCode_ReturnsAuthResponse()
    {
        // Arrange
        var code = "test-auth-code";
        var expectedResponse = new AuthResponse
        {
            AccessToken = "test-access-token",
            RefreshToken = "test-refresh-token",
            TokenType = "bearer",
            ExpiresIn = 3600,
            User = new SupabaseUser
            {
                Id = "test-user-id",
                Email = "<EMAIL>"
            }
        };

        var responseJson = JsonSerializer.Serialize(expectedResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.Method == HttpMethod.Post &&
                    req.RequestUri!.ToString().Contains("/auth/v1/token?grant_type=authorization_code")),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _supabaseClient.SignInWithGoogleAsync(code);

        // Assert
        result.Should().NotBeNull();
        result.AccessToken.Should().Be("test-access-token");
        result.RefreshToken.Should().Be("test-refresh-token");
        result.User.Should().NotBeNull();
        result.User!.Email.Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task SignInWithGoogleAsync_WithInvalidCode_ThrowsHttpRequestException()
    {
        // Arrange
        var code = "invalid-code";
        var errorResponse = new AuthErrorResponse
        {
            Error = "invalid_grant",
            ErrorDescription = "Invalid authorization code",
            Message = "Invalid authorization code"
        };

        var responseJson = JsonSerializer.Serialize(errorResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<HttpRequestException>(() => _supabaseClient.SignInWithGoogleAsync(code));
        exception.Message.Should().Contain("OAuth token exchange failed");
        exception.Message.Should().Contain("Invalid authorization code");
    }

    [Fact]
    public async Task SignInWithGoogleAsync_SendsCorrectRequestPayload()
    {
        // Arrange
        var code = "test-auth-code";
        var expectedResponse = new AuthResponse
        {
            AccessToken = "test-access-token",
            RefreshToken = "test-refresh-token"
        };

        var responseJson = JsonSerializer.Serialize(expectedResponse);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        HttpRequestMessage? capturedRequest = null;
        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .Callback<HttpRequestMessage, CancellationToken>((req, _) => capturedRequest = req)
            .ReturnsAsync(httpResponse);

        // Act
        await _supabaseClient.SignInWithGoogleAsync(code);

        // Assert
        capturedRequest.Should().NotBeNull();
        capturedRequest!.Method.Should().Be(HttpMethod.Post);
        capturedRequest.RequestUri!.ToString().Should().Contain("/auth/v1/token?grant_type=authorization_code");

        var requestContent = await capturedRequest.Content!.ReadAsStringAsync();
        var requestPayload = JsonSerializer.Deserialize<JsonElement>(requestContent);
        
        requestPayload.GetProperty("code").GetString().Should().Be(code);
        requestPayload.GetProperty("provider").GetString().Should().Be("google");
    }
}

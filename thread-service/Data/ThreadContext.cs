using Microsoft.EntityFrameworkCore;
using ThreadService.Models;

namespace ThreadService.Data
{
    public class ThreadContext : DbContext
    {
        public ThreadContext(DbContextOptions<ThreadContext> options) : base(options)
        {
        }

        public DbSet<Models.Thread> Threads { get; set; }
        public DbSet<Comment> Comments { get; set; }
        public DbSet<Like> Likes { get; set; }
        public DbSet<Share> Shares { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Thread entity
            modelBuilder.Entity<Models.Thread>(entity =>
            {
                entity.ToTable("threads");
                entity.HasKey(t => t.Id);
                entity.Property(t => t.Id).HasColumnName("id").HasColumnType("uuid");
                entity.Property(t => t.UserId).HasColumnName("user_id").HasColumnType("uuid").IsRequired();
                entity.Property(t => t.Content).HasColumnName("content").HasColumnType("text").IsRequired();
                entity.Property(t => t.CreatedAt).HasColumnName("created_at").HasColumnType("timestamp with time zone");
                entity.Property(t => t.UpdatedAt).HasColumnName("updated_at").HasColumnType("timestamp with time zone");

                entity.HasIndex(t => t.UserId);
                entity.HasIndex(t => t.CreatedAt);
            });

            // Configure Comment entity
            modelBuilder.Entity<Comment>(entity =>
            {
                entity.ToTable("comments");
                entity.HasKey(c => c.Id);
                entity.Property(c => c.Id).HasColumnName("id").HasColumnType("uuid");
                entity.Property(c => c.ThreadId).HasColumnName("thread_id").HasColumnType("uuid").IsRequired();
                entity.Property(c => c.UserId).HasColumnName("user_id").HasColumnType("uuid").IsRequired();
                entity.Property(c => c.Content).HasColumnName("content").HasColumnType("text").IsRequired();
                entity.Property(c => c.CreatedAt).HasColumnName("created_at").HasColumnType("timestamp with time zone");
                entity.Property(c => c.UpdatedAt).HasColumnName("updated_at").HasColumnType("timestamp with time zone");

                entity.HasIndex(c => c.ThreadId);
                entity.HasIndex(c => c.UserId);
                entity.HasIndex(c => c.CreatedAt);

                // Configure relationship
                entity.HasOne(c => c.Thread)
                    .WithMany(t => t.Comments)
                    .HasForeignKey(c => c.ThreadId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure Like entity
            modelBuilder.Entity<Like>(entity =>
            {
                entity.ToTable("likes");
                entity.HasKey(l => l.Id);
                entity.Property(l => l.Id).HasColumnName("id").HasColumnType("uuid");
                entity.Property(l => l.UserId).HasColumnName("user_id").HasColumnType("uuid").IsRequired();
                entity.Property(l => l.ThreadId).HasColumnName("thread_id").HasColumnType("uuid").IsRequired();
                entity.Property(l => l.CreatedAt).HasColumnName("created_at").HasColumnType("timestamp with time zone");

                entity.HasIndex(l => l.UserId);
                entity.HasIndex(l => l.ThreadId);
                entity.HasIndex(l => new { l.UserId, l.ThreadId }).IsUnique(); // Prevent duplicate likes

                // Configure relationship
                entity.HasOne(l => l.Thread)
                    .WithMany(t => t.Likes)
                    .HasForeignKey(l => l.ThreadId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure Share entity
            modelBuilder.Entity<Share>(entity =>
            {
                entity.ToTable("shares");
                entity.HasKey(s => s.Id);
                entity.Property(s => s.Id).HasColumnName("id").HasColumnType("uuid");
                entity.Property(s => s.UserId).HasColumnName("user_id").HasColumnType("uuid").IsRequired();
                entity.Property(s => s.ThreadId).HasColumnName("thread_id").HasColumnType("uuid").IsRequired();
                entity.Property(s => s.CreatedAt).HasColumnName("created_at").HasColumnType("timestamp with time zone");

                entity.HasIndex(s => s.UserId);
                entity.HasIndex(s => s.ThreadId);
                entity.HasIndex(s => new { s.UserId, s.ThreadId }).IsUnique(); // Prevent duplicate shares

                // Configure relationship
                entity.HasOne(s => s.Thread)
                    .WithMany(t => t.Shares)
                    .HasForeignKey(s => s.ThreadId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}

# ThreadService

## Setup for Development

### Prerequisites
1. .NET 10.0 SDK
2. Supabase account and project
3. Root `.env` file configured (see main README)

### Database Migration

The ThreadService uses a `ThreadContextFactory` that automatically loads environment variables from the root `.env` file.

**To run migrations:**
```bash
cd ThreadService
dotnet ef database update
```

**If migration fails:**
1. Ensure `.env` file exists in root directory
2. Verify `SUPABASE_CONNECTION_STRING` is set correctly
3. Check Supabase connection in dashboard

### Running the Service
```bash
cd ThreadService
dotnet run
```

The service will be available at:
- HTTP: http://localhost:5182
- HTTPS: https://localhost:7000

### API Documentation
Once running, visit: http://localhost:5182 for Swagger documentation

### Environment Variables Required
- `SUPABASE_CONNECTION_STRING` - Database connection
- `SUPABASE_URL` - Supabase project URL  
- `SUPABASE_JWT_SECRET` - JWT validation secret

All should be configured in the root `.env` file.
